# 一键修复所有问题的脚本

Write-Host "=" * 60 -ForegroundColor Green
Write-Host "一键修复 conda 和 PowerShell 问题" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Green

# 1. 修复 PowerShell 配置文件
Write-Host "`n步骤 1: 修复 PowerShell 配置文件" -ForegroundColor Yellow
Write-Host "-" * 40 -ForegroundColor Yellow

$profilePath = $PROFILE.CurrentUserCurrentHost
Write-Host "配置文件路径: $profilePath"

if (Test-Path $profilePath) {
    # 备份原文件
    $backupPath = "$profilePath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item $profilePath $backupPath
    Write-Host "✅ 原配置文件已备份到: $backupPath" -ForegroundColor Green
}

# 创建新的配置文件
$profileDir = Split-Path $profilePath -Parent
if (!(Test-Path $profileDir)) {
    New-Item -ItemType Directory -Path $profileDir -Force | Out-Null
}

$newProfileContent = @"
#region conda initialize
# !! Contents within this block are managed by 'conda init' !!
# 设置环境变量以避免编码问题
`$env:PYTHONIOENCODING = "utf-8"
`$env:CONDA_NO_PLUGINS = "true"
`$env:LANG = "en_US.UTF-8"

If (Test-Path "C:\ProgramData\anaconda3\Scripts\conda.exe") {
    try {
        (& "C:\ProgramData\anaconda3\Scripts\conda.exe" "shell.powershell" "hook") | Out-String | Where-Object {`$_} | Invoke-Expression
    } catch {
        Write-Warning "Conda 初始化失败，使用备用方案"
        # 直接添加 conda 到 PATH
        `$condaPath = "C:\ProgramData\anaconda3\Scripts"
        if (`$env:PATH -notlike "*`$condaPath*") {
            `$env:PATH = "`$condaPath;" + `$env:PATH
        }
    }
}
#endregion
"@

$newProfileContent | Out-File $profilePath -Encoding UTF8 -Force
Write-Host "✅ PowerShell 配置文件已修复" -ForegroundColor Green

# 2. 修复 conda 配置
Write-Host "`n步骤 2: 修复 conda 配置" -ForegroundColor Yellow
Write-Host "-" * 40 -ForegroundColor Yellow

# 设置环境变量
$env:PYTHONIOENCODING = "utf-8"
$env:CONDA_NO_PLUGINS = "true"
$env:LANG = "en_US.UTF-8"

# 修复 .condarc 文件
$condarcPath = "$env:USERPROFILE\.condarc"
if (Test-Path $condarcPath) {
    $backupPath = "$condarcPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item $condarcPath $backupPath
    Write-Host "✅ .condarc 文件已备份到: $backupPath" -ForegroundColor Green
}

$newCondarcContent = @"
channels:
  - defaults
  - conda-forge
show_channel_urls: true
report_errors: false
ssl_verify: true
"@

$newCondarcContent | Out-File $condarcPath -Encoding UTF8 -Force
Write-Host "✅ .condarc 文件已修复" -ForegroundColor Green

# 3. 测试修复结果
Write-Host "`n步骤 3: 测试修复结果" -ForegroundColor Yellow
Write-Host "-" * 40 -ForegroundColor Yellow

# 重新加载配置文件
try {
    . $PROFILE
    Write-Host "✅ PowerShell 配置文件重新加载成功" -ForegroundColor Green
} catch {
    Write-Host "⚠️  PowerShell 配置文件重新加载失败，请重启 PowerShell" -ForegroundColor Yellow
}

# 测试 Python
Write-Host "`n测试 Python..."
try {
    $pythonVersion = & python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Python 工作正常: $pythonVersion" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Python 命令有问题，尝试使用完整路径" -ForegroundColor Yellow
        $pythonVersion = & "C:\ProgramData\anaconda3\python.exe" --version 2>&1
        Write-Host "✅ Python (完整路径) 工作正常: $pythonVersion" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Python 测试失败" -ForegroundColor Red
}

# 4. 提供使用建议
Write-Host "`n" + "=" * 60 -ForegroundColor Green
Write-Host "修复完成！使用建议:" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Green

Write-Host "`n如果 conda 仍有问题，可以使用以下方式:" -ForegroundColor Yellow
Write-Host "1. 重启 PowerShell 窗口" -ForegroundColor Cyan
Write-Host "2. 使用虚拟环境:" -ForegroundColor Cyan
Write-Host "   E:/project_LLM_api/venv/Scripts/Activate.ps1" -ForegroundColor White
Write-Host "3. 直接使用 Python:" -ForegroundColor Cyan
Write-Host "   C:\ProgramData\anaconda3\python.exe your_script.py" -ForegroundColor White
Write-Host "4. 测试 OpenAI 库:" -ForegroundColor Cyan
Write-Host "   python test_openai_direct.py" -ForegroundColor White

Write-Host "`n现在可以尝试运行你的 OpenAI 脚本了！" -ForegroundColor Green
