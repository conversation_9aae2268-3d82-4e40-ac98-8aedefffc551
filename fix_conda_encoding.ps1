# 修复 conda 编码问题的脚本

Write-Host "正在修复 conda 编码问题..." -ForegroundColor Yellow

# 设置环境变量
Write-Host "设置环境变量..." -ForegroundColor Cyan
$env:PYTHONIOENCODING = "utf-8"
$env:CONDA_NO_PLUGINS = "true"
$env:LANG = "en_US.UTF-8"
$env:LC_ALL = "en_US.UTF-8"

# 检查 .condarc 文件
$condarcPath = "$env:USERPROFILE\.condarc"
Write-Host "检查 .condarc 文件: $condarcPath" -ForegroundColor Cyan

if (Test-Path $condarcPath) {
    Write-Host "找到 .condarc 文件，正在备份..." -ForegroundColor Green
    $backupPath = "$condarcPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item $condarcPath $backupPath
    Write-Host "备份完成: $backupPath" -ForegroundColor Green
    
    # 检查文件编码
    try {
        $content = Get-Content $condarcPath -Raw -Encoding UTF8
        Write-Host ".condarc 文件读取成功" -ForegroundColor Green
    } catch {
        Write-Host "读取 .condarc 文件时出错，尝试重新创建..." -ForegroundColor Yellow
        
        # 创建一个简单的 .condarc 文件
        $newCondarcContent = @"
channels:
  - defaults
  - conda-forge
show_channel_urls: true
report_errors: false
"@
        $newCondarcContent | Out-File $condarcPath -Encoding UTF8 -Force
        Write-Host "新的 .condarc 文件已创建" -ForegroundColor Green
    }
} else {
    Write-Host ".condarc 文件不存在，创建新文件..." -ForegroundColor Yellow
    $newCondarcContent = @"
channels:
  - defaults
  - conda-forge
show_channel_urls: true
report_errors: false
"@
    $newCondarcContent | Out-File $condarcPath -Encoding UTF8 -Force
    Write-Host "新的 .condarc 文件已创建" -ForegroundColor Green
}

# 测试 conda 命令
Write-Host "`n测试 conda 命令..." -ForegroundColor Cyan
try {
    $condaVersion = & conda --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Conda 工作正常: $condaVersion" -ForegroundColor Green
    } else {
        Write-Host "Conda 仍有问题，但可以继续使用 Python" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Conda 命令执行失败，但可以直接使用 Python" -ForegroundColor Yellow
}

Write-Host "`n修复完成！" -ForegroundColor Green
Write-Host "如果 conda 仍有问题，可以直接使用以下方式运行 Python:" -ForegroundColor Yellow
Write-Host "1. 使用完整路径: C:\ProgramData\anaconda3\python.exe" -ForegroundColor Cyan
Write-Host "2. 或者激活虚拟环境: E:/project_LLM_api/venv/Scripts/Activate.ps1" -ForegroundColor Cyan
