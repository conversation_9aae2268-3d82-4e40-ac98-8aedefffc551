@echo off
echo Checking Python and OpenAI installation...
echo.

echo === Python Version ===
python --version
echo.

echo === Pip List (OpenAI related) ===
python -m pip list | findstr -i openai
echo.

echo === Direct Import Test ===
python -c "import openai; print('OpenAI version:', openai.__version__); print('Import successful!')"
echo.

echo === Installation Location ===
python -c "import openai; print('OpenAI location:', openai.__file__)"
echo.

pause
