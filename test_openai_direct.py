#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试 OpenAI 库，绕过 conda 问题
"""

import sys
import os

# 设置编码环境变量
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['CONDA_NO_PLUGINS'] = 'true'

print("=" * 60)
print("OpenAI 库测试 (绕过 conda 问题)")
print("=" * 60)

print(f"Python 可执行文件: {sys.executable}")
print(f"Python 版本: {sys.version}")
print(f"当前工作目录: {os.getcwd()}")

print("\n" + "-" * 40)
print("测试 OpenAI 库导入...")

try:
    # 测试导入 openai 库
    import openai
    print("✅ 成功导入 openai 库!")
    print(f"   版本: {openai.__version__}")
    print(f"   位置: {openai.__file__}")
    
    # 测试导入 OpenAI 类
    try:
        from openai import OpenAI
        print("✅ 成功导入 OpenAI 类!")
        
        # 测试创建客户端实例（不进行实际 API 调用）
        try:
            # 使用一个测试 API key
            test_client = OpenAI(api_key="test-key")
            print("✅ 成功创建 OpenAI 客户端实例!")
            print("   OpenAI 库安装正确，可以正常使用")
            
        except Exception as e:
            print(f"⚠️  创建客户端时出错: {e}")
            print("   这可能是正常的，因为使用了测试 API key")
            
    except ImportError as e:
        print(f"❌ 导入 OpenAI 类失败: {e}")
        
except ImportError as e:
    print(f"❌ 导入 openai 库失败: {e}")
    print("\n解决方案:")
    print("1. 安装 openai 库:")
    print("   pip install openai")
    print("2. 或者在虚拟环境中安装:")
    print("   E:/project_LLM_api/venv/Scripts/activate")
    print("   pip install openai")
    
except Exception as e:
    print(f"❌ 意外错误: {e}")

print("\n" + "-" * 40)
print("检查已安装的包...")

try:
    import subprocess
    result = subprocess.run([sys.executable, "-m", "pip", "list"], 
                          capture_output=True, text=True, encoding='utf-8')
    
    if result.returncode == 0:
        lines = result.stdout.split('\n')
        openai_packages = [line for line in lines if 'openai' in line.lower()]
        
        if openai_packages:
            print("找到 OpenAI 相关包:")
            for pkg in openai_packages:
                print(f"   {pkg}")
        else:
            print("❌ 未找到 OpenAI 相关包")
            print("   需要安装: pip install openai")
    else:
        print(f"❌ 检查包列表失败: {result.stderr}")
        
except Exception as e:
    print(f"❌ 检查包时出错: {e}")

print("\n" + "=" * 60)
print("测试完成")

# 如果是直接运行，暂停以便查看结果
if __name__ == "__main__":
    input("\n按 Enter 键退出...")
