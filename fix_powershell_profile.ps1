# 修复 PowerShell 配置文件的脚本

Write-Host "正在修复 PowerShell 配置文件..." -ForegroundColor Yellow

# 获取当前用户的 PowerShell 配置文件路径
$profilePath = $PROFILE.CurrentUserCurrentHost
Write-Host "配置文件路径: $profilePath" -ForegroundColor Cyan

# 检查配置文件是否存在
if (Test-Path $profilePath) {
    Write-Host "找到配置文件，正在备份..." -ForegroundColor Green
    
    # 备份原文件
    $backupPath = "$profilePath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item $profilePath $backupPath
    Write-Host "备份完成: $backupPath" -ForegroundColor Green
    
    # 读取原文件内容
    $content = Get-Content $profilePath -Raw -Encoding UTF8
    
    # 修复语法错误
    $fixedContent = $content -replace '\?\{\$_\}', 'Where-Object {$_}'
    
    # 写入修复后的内容
    $fixedContent | Out-File $profilePath -Encoding UTF8
    Write-Host "配置文件已修复" -ForegroundColor Green
} else {
    Write-Host "配置文件不存在: $profilePath" -ForegroundColor Red
}

# 创建一个新的、简化的配置文件
$newProfileContent = @"
#region conda initialize
# !! Contents within this block are managed by 'conda init' !!
# 设置环境变量以避免编码问题
`$env:PYTHONIOENCODING = "utf-8"
`$env:CONDA_NO_PLUGINS = "true"

If (Test-Path "C:\ProgramData\anaconda3\Scripts\conda.exe") {
    try {
        (& "C:\ProgramData\anaconda3\Scripts\conda.exe" "shell.powershell" "hook") | Out-String | Where-Object {`$_} | Invoke-Expression
    } catch {
        Write-Warning "Conda 初始化失败，但不影响 Python 使用"
    }
}
#endregion
"@

# 确保目录存在
$profileDir = Split-Path $profilePath -Parent
if (!(Test-Path $profileDir)) {
    New-Item -ItemType Directory -Path $profileDir -Force
    Write-Host "创建配置文件目录: $profileDir" -ForegroundColor Green
}

# 写入新的配置文件
$newProfileContent | Out-File $profilePath -Encoding UTF8 -Force
Write-Host "新的配置文件已创建" -ForegroundColor Green

Write-Host "`n修复完成！请重新启动 PowerShell 或运行以下命令重新加载配置:" -ForegroundColor Yellow
Write-Host ". `$PROFILE" -ForegroundColor Cyan
