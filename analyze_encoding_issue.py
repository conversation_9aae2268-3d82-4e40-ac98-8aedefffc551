#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析 conda 编码问题的脚本
"""

import os
import sys
import locale

print("=" * 60)
print("conda 编码问题分析")
print("=" * 60)

# 1. 系统编码信息
print("\n1. 系统编码信息:")
print(f"   系统默认编码: {sys.getdefaultencoding()}")
print(f"   文件系统编码: {sys.getfilesystemencoding()}")
print(f"   locale 编码: {locale.getpreferredencoding()}")
print(f"   标准输出编码: {sys.stdout.encoding}")

# 2. 环境变量
print("\n2. 相关环境变量:")
encoding_vars = ['PYTHONIOENCODING', 'LANG', 'LC_ALL', 'CONDA_NO_PLUGINS']
for var in encoding_vars:
    value = os.environ.get(var, '未设置')
    print(f"   {var}: {value}")

# 3. 分析 0x95 字节
print("\n3. 0x95 字节分析:")
print("   0x95 在不同编码中的含义:")

# 0x95 在不同编码中的表示
byte_95 = b'\x95'

try:
    # 尝试不同编码解码
    encodings = ['gbk', 'utf-8', 'cp936', 'latin1']
    for encoding in encodings:
        try:
            decoded = byte_95.decode(encoding)
            print(f"   {encoding}: '{decoded}' (成功)")
        except UnicodeDecodeError as e:
            print(f"   {encoding}: 解码失败 - {e}")
except Exception as e:
    print(f"   解码测试失败: {e}")

# 4. 中文字符编码测试
print("\n4. 中文字符编码测试:")
chinese_text = "劳动竞赛"
print(f"   原文: {chinese_text}")

encodings = ['utf-8', 'gbk', 'cp936']
for encoding in encodings:
    try:
        encoded = chinese_text.encode(encoding)
        print(f"   {encoding} 编码: {encoded.hex()} ({len(encoded)} 字节)")
        
        # 检查是否包含 0x95
        if b'\x95' in encoded:
            pos = encoded.find(b'\x95')
            print(f"     ⚠️  包含 0x95 字节，位置: {pos}")
    except Exception as e:
        print(f"   {encoding} 编码失败: {e}")

# 5. 路径编码测试
print("\n5. 路径编码测试:")
problematic_paths = [
    "C:\\Users\\<USER>\\Desktop\\2025劳动竞赛",
    "D:\\新建文件夹\\cursor",
    os.path.expanduser("~")
]

for path in problematic_paths:
    print(f"   路径: {path}")
    if os.path.exists(path):
        print("     ✅ 路径存在")
    else:
        print("     ❌ 路径不存在")
    
    # 测试路径编码
    for encoding in ['utf-8', 'gbk']:
        try:
            encoded = path.encode(encoding)
            if b'\x95' in encoded:
                pos = encoded.find(b'\x95')
                print(f"     ⚠️  {encoding} 编码包含 0x95，位置: {pos}")
        except Exception as e:
            print(f"     {encoding} 编码失败: {e}")

# 6. conda 配置文件检查
print("\n6. conda 配置文件检查:")
config_files = [
    os.path.expanduser("~/.condarc"),
    "C:\\ProgramData\\anaconda3\\etc\\conda\\condarc",
    os.path.join(os.environ.get('CONDA_ROOT', ''), 'etc', 'conda', 'condarc')
]

for config_file in config_files:
    if os.path.exists(config_file):
        print(f"   配置文件: {config_file}")
        try:
            with open(config_file, 'rb') as f:
                content = f.read()
                print(f"     文件大小: {len(content)} 字节")
                
                # 检查是否包含 0x95
                if b'\x95' in content:
                    positions = [i for i, b in enumerate(content) if b == 0x95]
                    print(f"     ⚠️  包含 0x95 字节，位置: {positions}")
                    
                    # 显示问题位置的上下文
                    for pos in positions[:3]:  # 只显示前3个
                        start = max(0, pos - 10)
                        end = min(len(content), pos + 10)
                        context = content[start:end]
                        print(f"       位置 {pos} 上下文: {context}")
                else:
                    print("     ✅ 未发现 0x95 字节")
                    
        except Exception as e:
            print(f"     ❌ 读取失败: {e}")
    else:
        print(f"   配置文件不存在: {config_file}")

print("\n" + "=" * 60)
print("分析完成")
print("\n可能的解决方案:")
print("1. 避免在路径中使用中文字符")
print("2. 设置正确的环境变量 (PYTHONIOENCODING=utf-8)")
print("3. 重新创建 conda 配置文件")
print("4. 使用虚拟环境避免系统级 conda 问题")
